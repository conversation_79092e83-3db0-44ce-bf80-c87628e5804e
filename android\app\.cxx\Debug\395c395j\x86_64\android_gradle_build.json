{"buildFiles": ["E:\\aymen\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\aymen\\pfa_mobile\\android\\app\\.cxx\\Debug\\395c395j\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\aymen\\pfa_mobile\\android\\app\\.cxx\\Debug\\395c395j\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Android\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Android\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}