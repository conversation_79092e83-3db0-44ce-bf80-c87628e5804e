{"buildFiles": ["E:\\aymen\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\aymen\\pfa_mobile\\android\\app\\.cxx\\Debug\\k232z702\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\aymen\\pfa_mobile\\android\\app\\.cxx\\Debug\\k232z702\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Android\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Android\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}