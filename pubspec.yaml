name: pfa_mobile
description: A mobile application for iris analysis and pattern recognition

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  camera: ^0.11.1
  camera_android: ^0.10.10+2
  camera_avfoundation: ^0.9.18+14
  camera_web: ^0.3.5
  flutter_plugin_android_lifecycle: ^2.0.28
  path_provider: ^2.0.15
  shared_preferences: ^2.2.0
  http: ^1.1.0
  provider: ^6.0.5
  firebase_auth: ^5.5.2
  firebase_core: ^3.13.0
  cloud_firestore: ^5.5.0
  image_picker: ^1.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true

  assets:
    - images/
    - assets/
  fonts:
    - family: Playfair Display
      fonts:
        - asset: assets/fonts/PlayfairDisplay-Regular.ttf
        - asset: assets/fonts/PlayfairDisplay-Bold.ttf
          weight: 700
    - family: Montserrat
      fonts:
        - asset: assets/fonts/Montserrat-Regular.ttf
        - asset: assets/fonts/Montserrat-Medium.ttf
          weight: 500
        - asset: assets/fonts/Montserrat-SemiBold.ttf
          weight: 600

