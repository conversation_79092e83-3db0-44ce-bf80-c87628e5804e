// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAkdthQO0aaXLR-wVaYYB5C_0HgSESWGns',
    appId: '1:770202078532:web:ae40074feaca3f62011b11',
    messagingSenderId: '770202078532',
    projectId: 'profilingiris',
    authDomain: 'profilingiris.firebaseapp.com',
    storageBucket: 'profilingiris.firebasestorage.app',
    measurementId: 'G-ER3TYLNDY7',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBYQyz99Po4zvQlu58y1tzNBxBve6GG8MA',
    appId: '1:770202078532:android:df38f9181561ce9b011b11',
    messagingSenderId: '770202078532',
    projectId: 'profilingiris',
    storageBucket: 'profilingiris.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBcD1oEN-zRTURIEqORH8ZmnnSuZz1_BTk',
    appId: '1:83392557786:ios:7e4a5bfe6c3863d72aabe3',
    messagingSenderId: '83392557786',
    projectId: 'pfamobile-32a1b',
    storageBucket: 'pfamobile-32a1b.firebasestorage.app',
    iosBundleId: 'com.example.pfaMobile',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBcD1oEN-zRTURIEqORH8ZmnnSuZz1_BTk',
    appId: '1:83392557786:ios:7e4a5bfe6c3863d72aabe3',
    messagingSenderId: '83392557786',
    projectId: 'pfamobile-32a1b',
    storageBucket: 'pfamobile-32a1b.firebasestorage.app',
    iosBundleId: 'com.example.pfaMobile',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyDlpE0ecEpVuiQjaXvgCilntDCVl3mWmlg',
    appId: '1:83392557786:web:03c2ceaf262425a02aabe3',
    messagingSenderId: '83392557786',
    projectId: 'pfamobile-32a1b',
    authDomain: 'pfamobile-32a1b.firebaseapp.com',
    storageBucket: 'pfamobile-32a1b.firebasestorage.app',
    measurementId: 'G-6CD2E9PPEJ',
  );

}