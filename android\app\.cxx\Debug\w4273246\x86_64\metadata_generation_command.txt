                        -HE:\aymen\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-D<PERSON>DROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Android\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Android\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Android\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Android\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=E:\aymen\pfa\pfa_mobile\build\app\intermediates\cxx\Debug\w4273246\obj\x86_64
-DC<PERSON>KE_RUNTIME_OUTPUT_DIRECTORY=E:\aymen\pfa\pfa_mobile\build\app\intermediates\cxx\Debug\w4273246\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BE:\aymen\pfa\pfa_mobile\android\app\.cxx\Debug\w4273246\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2