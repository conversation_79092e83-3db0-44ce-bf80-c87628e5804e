{"buildFiles": ["E:\\aymen\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\aymen\\pfa\\pfa_mobile\\android\\app\\.cxx\\Debug\\w4273246\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\aymen\\pfa\\pfa_mobile\\android\\app\\.cxx\\Debug\\w4273246\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}