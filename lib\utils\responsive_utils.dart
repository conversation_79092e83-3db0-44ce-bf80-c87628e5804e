import 'package:flutter/material.dart';

/// Utility class for responsive design helpers
class ResponsiveUtils {
  /// Check if the device is in landscape orientation
  static bool isLandscape(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.width > size.height;
  }

  /// Check if the screen is considered small (mobile)
  static bool isSmallScreen(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return size.width < 600;
  }

  /// Get responsive spacing based on orientation
  static double getResponsiveSpacing(BuildContext context, {
    double portraitSpacing = 0.03,
    double landscapeSpacing = 0.02,
  }) {
    final size = MediaQuery.of(context).size;
    final isLandscape = ResponsiveUtils.isLandscape(context);
    return size.height * (isLandscape ? landscapeSpacing : portraitSpacing);
  }

  /// Get responsive height based on orientation
  static double getResponsiveHeight(BuildContext context, {
    double portraitHeight = 0.3,
    double landscapeHeight = 0.25,
  }) {
    final size = MediaQuery.of(context).size;
    final isLandscape = ResponsiveUtils.isLandscape(context);
    return size.height * (isLandscape ? landscapeHeight : portraitHeight);
  }

  /// Get responsive font size
  static double getResponsiveFontSize(BuildContext context, {
    double smallScreenSize = 0.04,
    double largeScreenSize = 0.03,
  }) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);
    return size.width * (isSmallScreen ? smallScreenSize : largeScreenSize);
  }

  /// Create a responsive SizedBox for spacing
  static Widget responsiveSpacing(BuildContext context, {
    double portraitSpacing = 0.03,
    double landscapeSpacing = 0.02,
  }) {
    return SizedBox(
      height: getResponsiveSpacing(
        context,
        portraitSpacing: portraitSpacing,
        landscapeSpacing: landscapeSpacing,
      ),
    );
  }

  /// Wrap content in a responsive scrollable container
  static Widget responsiveScrollableColumn({
    required List<Widget> children,
    EdgeInsetsGeometry? padding,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
  }) {
    return SingleChildScrollView(
      padding: padding,
      child: Column(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        children: children,
      ),
    );
  }

  /// Create a responsive container that adapts to orientation
  static Widget responsiveContainer({
    required BuildContext context,
    required Widget child,
    double? portraitHeight,
    double? landscapeHeight,
    EdgeInsetsGeometry? padding,
    Decoration? decoration,
  }) {
    final isLandscape = ResponsiveUtils.isLandscape(context);
    final size = MediaQuery.of(context).size;
    
    double? height;
    if (portraitHeight != null && landscapeHeight != null) {
      height = size.height * (isLandscape ? landscapeHeight : portraitHeight);
    }

    return Container(
      height: height,
      padding: padding,
      decoration: decoration,
      child: child,
    );
  }
}

/// Extension on BuildContext for easier access to responsive utilities
extension ResponsiveContext on BuildContext {
  bool get isLandscape => ResponsiveUtils.isLandscape(this);
  bool get isSmallScreen => ResponsiveUtils.isSmallScreen(this);
  
  double responsiveSpacing({
    double portraitSpacing = 0.03,
    double landscapeSpacing = 0.02,
  }) => ResponsiveUtils.getResponsiveSpacing(
    this,
    portraitSpacing: portraitSpacing,
    landscapeSpacing: landscapeSpacing,
  );

  double responsiveHeight({
    double portraitHeight = 0.3,
    double landscapeHeight = 0.25,
  }) => ResponsiveUtils.getResponsiveHeight(
    this,
    portraitHeight: portraitHeight,
    landscapeHeight: landscapeHeight,
  );

  double responsiveFontSize({
    double smallScreenSize = 0.04,
    double largeScreenSize = 0.03,
  }) => ResponsiveUtils.getResponsiveFontSize(
    this,
    smallScreenSize: smallScreenSize,
    largeScreenSize: largeScreenSize,
  );
}
