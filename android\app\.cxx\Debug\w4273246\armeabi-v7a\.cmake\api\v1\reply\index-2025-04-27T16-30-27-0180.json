{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Android/cmake/3.22.1/bin/cmake.exe", "cpack": "C:/Android/cmake/3.22.1/bin/cpack.exe", "ctest": "C:/Android/cmake/3.22.1/bin/ctest.exe", "root": "C:/Android/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-12c91e94e7bcda9dfd51.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-ea74112ebd787fb45608.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-27ad8b5fce07be5d9470.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-ea74112ebd787fb45608.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-27ad8b5fce07be5d9470.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-12c91e94e7bcda9dfd51.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}